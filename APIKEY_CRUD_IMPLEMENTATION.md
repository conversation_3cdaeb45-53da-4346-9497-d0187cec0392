# API Key CRUD Implementation Summary

## Tổng quan
Đã tạo đầy đủ hệ thống CRUD cho entity ApiKey theo pattern của <PERSON>, bao gồm tất cả các thành phần cần thiết cho việc quản lý API Key trong hệ thống.

## C<PERSON>c thành phần đã tạo

### 1. Constants và Permissions
- **File**: `CoreService/Core.Shared/Constants/Permissions.cs`
  - Thêm permissions: `API_KEY_VIEW`, `API_KEY_ADD`, `API_KEY_EDIT`, `API_KEY_DELETE`

- **File**: `CoreService/Core.Shared/Constants/Constant.cs`
  - Thêm log constants: `ACTION_API_KEY_CREATE`, `ACTION_API_KEY_UPDATE`, `ACTION_API_KEY_DELETE`
  - Thêm cache constant: `API_KEY = "api_key"`

- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyConstant.cs`
  - Cache management constants và helper methods

### 2. Models và DTOs
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyModel.cs`
  - `ApiKeyBaseModel` - model cơ bản
  - `ApiKeyModel` - model đầy đủ
  - `CreateApiKeyModel` - DTO cho tạo mới
  - `UpdateApiKeyModel` - DTO cho cập nhật với method `UpdateEntity()`
  - `ApiKeySelectItemModel` - model cho combobox
  - `ApiKeyQueryFilter` - filter cho query với các điều kiện lọc

### 3. Commands (Business Logic)
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyCommands/CreateApiKeyCommand.cs`
  - Tạo mới API Key với validation key unique
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyCommands/UpdateApiKeyCommand.cs`
  - Cập nhật API Key với validation
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyCommands/DeleteApiKeyCommand.cs`
  - Xóa API Key
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyCommands/GenerateApiKeyCommand.cs`
  - Tạo API Key tự động với format: `ak_{timestamp}_{random}`
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyCommands/RevokeApiKeyCommand.cs`
  - Vô hiệu hóa API Key (set IsActive = false)

### 4. Queries
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyQueries/GetApiKeyByIdQuery.cs`
  - Lấy API Key theo ID với cache
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyQueries/GetFilterApiKeyQuery.cs`
  - Lấy danh sách API Key có phân trang và filter
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyQueries/GetComboboxApiKeyQuery.cs`
  - Lấy danh sách cho combobox
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyQueries/GetApiKeyStatisticsQuery.cs`
  - Thống kê API Key (tổng số, active, expired, expiring, theo user, theo tháng)
  
- **File**: `CoreService/Core.Business/System/ApiKey/ApiKeyQueries/ValidateApiKeyQuery.cs`
  - Validate API Key với các điều kiện: tồn tại, active, trong thời hạn

### 5. Controller
- **File**: `APIService/System.API/Controller/System/ApiKeyController.cs`
  - Đầy đủ các endpoint CRUD:
    - `POST /api/v1/ApiKey` - Tạo mới
    - `PUT /api/v1/ApiKey` - Cập nhật
    - `DELETE /api/v1/ApiKey/{id}` - Xóa
    - `GET /api/v1/ApiKey/{id}` - Lấy theo ID
    - `POST /api/v1/ApiKey/filter` - Lấy danh sách có filter
    - `GET /api/v1/ApiKey/combobox` - Lấy cho combobox
    - `POST /api/v1/ApiKey/generate` - Tạo API Key tự động
    - `PUT /api/v1/ApiKey/{id}/revoke` - Vô hiệu hóa
    - `GET /api/v1/ApiKey/validate` - Validate (public)
    - `POST /api/v1/ApiKey/validate` - Validate (authenticated)
    - `GET /api/v1/ApiKey/statistics` - Thống kê

### 6. Database Context Updates
- **File**: `CoreService/Core.Data/Systems/DataContext/System/SystemDataContext.cs`
  - Thêm `DbSet<ApiKey> ApiKeys`
  
- **File**: `CoreService/Core.Data/Systems/DataContext/System/SystemReadDataContext.cs`
  - Thêm `DbSet<ApiKey> ApiKeys`

### 7. Authentication & Security
- **File**: `CoreService/Core.API.Shared/Attributes/ApiKeyAuthenticationAttribute.cs`
  - Attribute để validate API Key từ header `X-API-Key`
  - Kiểm tra tồn tại, active, thời hạn
  - Thêm thông tin vào HttpContext

- **File**: `APIService/System.API/Controller/System/ApiKeyExampleController.cs`
  - Demo controller sử dụng `[ApiKeyAuthentication]` attribute
  - Các endpoint protected và public

### 8. Localization
- **File**: `APIService/System.API/wwwroot/Localization/vi-VN.json`
- **File**: `APIService/System.API/wwwroot/Localization/en-US.json`
  - Thêm các message keys:
    - `api-key.key.required`
    - `api-key.key.max-length`
    - `api-key.key.existed`
    - `api-key.invalid`

## Tính năng chính

### CRUD Operations
- ✅ Create: Tạo mới API Key với validation
- ✅ Read: Lấy thông tin, danh sách có filter, combobox
- ✅ Update: Cập nhật thông tin API Key
- ✅ Delete: Xóa API Key

### Advanced Features
- ✅ Generate: Tự động tạo API Key unique
- ✅ Revoke: Vô hiệu hóa API Key
- ✅ Validate: Kiểm tra tính hợp lệ
- ✅ Statistics: Thống kê chi tiết
- ✅ Authentication: Middleware/Attribute để authenticate bằng API Key
- ✅ Cache Management: Tự động clear cache khi có thay đổi
- ✅ Logging: Ghi log tất cả operations
- ✅ Localization: Hỗ trợ đa ngôn ngữ

### Security Features
- ✅ API Key format: `ak_{timestamp}_{random}` để tránh collision
- ✅ Validation: Kiểm tra tồn tại, active, thời hạn
- ✅ Permission-based access control
- ✅ Audit logging cho tất cả operations

## Cách sử dụng

### 1. Tạo API Key
```http
POST /api/v1/ApiKey/generate
{
  "userId": 1,
  "validFrom": "2024-01-01T00:00:00",
  "validTo": "2024-12-31T23:59:59",
  "description": "API Key for external integration"
}
```

### 2. Sử dụng API Key
```http
GET /api/v1/ApiKeyExample/protected
Headers:
  X-API-Key: ak_1640995200_abcd1234efgh5678
```

### 3. Validate API Key
```http
GET /api/v1/ApiKey/validate?apiKey=ak_1640995200_abcd1234efgh5678
```

## Lưu ý
- Entity ApiKey kế thừa từ `BaseTableDefault` nên đã có sẵn các trường: Id, Order, IsActive, Description, CreatedDate, CreatedUserId, ModifiedDate, ModifiedUserId
- Tất cả operations đều có logging và cache management
- Permissions được định nghĩa theo pattern của hệ thống
- Tuân thủ coding conventions và architecture patterns hiện tại
- Không chạy migration database (theo yêu cầu)
