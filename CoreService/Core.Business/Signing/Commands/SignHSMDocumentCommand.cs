using System;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared;
using Core.Shared.Enums;
using MediatR;
using Org.BouncyCastle.X509;
using Serilog;
using VisnamSignServerClient;

namespace Core.Business.Signing;

public class SignHSMDocumentCommand : IRequest<SignDocumentResponseModel>
{
    public SignDocumentRequestModel SignDocumentRequestModel { get; set; }

    public SignHSMDocumentCommand(SignDocumentRequestModel signDocumentRequestModel)
    {
        SignDocumentRequestModel = signDocumentRequestModel;
    }

    public class Handler : IRequestHandler<SignHSMDocumentCommand, SignDocumentResponseModel>
    {
        private readonly IMediator _mediator;
        private readonly VisnamSignServerApiClient _client;

        public Handler(IMediator mediator, VisnamSignServerApiClient client)
        {
            _client = client;
            _mediator = mediator;
        }

        public async Task<SignDocumentResponseModel> Handle(SignHSMDocumentCommand request,
            CancellationToken cancellationToken)
        {
            var model = request.SignDocumentRequestModel;
            Log.Information($"Ký HSM: {model.ChungThuSoId} - {model.MauChuKyId}");

            if (string.IsNullOrEmpty(model.FileBase64))
            {
                throw new ArgumentException("File ký không được để trống");
            }

            // Lấy thông tin CTS  
            var cts = await _mediator.Send(new GetChungThuSoByIdQuery(model.ChungThuSoId));
            if (cts == null)
            {
                throw new ArgumentException("Chứng thư số không tồn tại");
            }

            // Nếu không truyền cert sang thì thấy thông tin cert từ RA Service
            X509CertificateParser parser = new X509CertificateParser();
            X509Certificate[] chain;

            chain = new X509Certificate[3];
            chain[0] = parser.ReadCertificate(Convert.FromBase64String(cts.CertificateBase64));
            
            if (!string.IsNullOrEmpty(cts.CACertificateBase64))
            {
                chain[1] = parser.ReadCertificate(Convert.FromBase64String(cts.CACertificateBase64));
            }

            if (!string.IsNullOrEmpty(cts.CACertificateBase64))
            {
                chain[2] = parser.ReadCertificate(Convert.FromBase64String(cts.CACertificateBase64));
            }

            // Kiểm tra cert  
            foreach (var item in chain)
            {
                // Kiểm tra thời gian hiệu lực  
                if (!item.IsValidNow)
                {
                    Log.Information($"Chứng thư số hết hiệu lực");
                    throw new ArgumentException("Chứng thư số đã hết hiệu lực");
                }
            }

            MauChuKyModel mauChuKy = null;
            if (model.SignatureAppearanceConfiguration.IsVisible && model.MauChuKyId == null)
            {
                throw new ArgumentException("Mẫu chữ ký không được để trống");
            }
            else
            {
                // Lấy thông tin mẫu chữ ký  
                mauChuKy = await _mediator.Send(new GetMauChuKyByIdQuery(model.MauChuKyId.Value));
                if (mauChuKy == null)
                {
                    throw new ArgumentException("Mẫu chữ ký không tồn tại");
                }
            }

            #region Thực hiện ký số

            try
            {
                // Kiểm tra ReferenceId
                if (cts.ReferenceId <= 0)
                {
                    throw new ArgumentException("Chứng thư số chưa được liên kết với tài khoản Visnam");
                }

                // Lấy thông tin tài khoản kết nối Visnam
                var visnamAccount = await _mediator.Send(new GetVisnamTaiKhoanKetNoiByIdQuery(cts.ReferenceId));
                if (visnamAccount == null)
                {
                    throw new ArgumentException(
                        $"Không tìm thấy tài khoản kết nối Visnam với ReferenceId: {cts.ReferenceId}");
                }

                // Kiểm tra dữ liệu cần thiết
                if (string.IsNullOrEmpty(visnamAccount.Key))
                {
                    throw new ArgumentException("Key của tài khoản Visnam không được để trống");
                }

                if (string.IsNullOrEmpty(visnamAccount.Secret))
                {
                    throw new ArgumentException("Secret của tài khoản Visnam không được để trống");
                }

                // Decrypt secret
                string decryptedSecret;
                try
                {
                    decryptedSecret = AesEncryption.Decrypt(visnamAccount.Secret, AesEncryption.KeyDefault);
                    if (string.IsNullOrEmpty(decryptedSecret))
                    {
                        throw new Exception("Secret sau khi decrypt bị rỗng");
                    }
                }
                catch (Exception ex)
                {
                    throw new CryptographicException($"Lỗi khi decrypt secret: {ex.Message}. " +
                                                     $"Secret length: {visnamAccount.Secret?.Length ?? 0}, " +
                                                     $"Secret preview: {visnamAccount.Secret?.Substring(0, Math.Min(20, visnamAccount.Secret?.Length ?? 0))}...",
                        ex);
                }

                // Tạo request cho SignPDFSync
                var signPdfRequest = new SignPDFSyncRequestModel
                {
                    Base64Pdf = model.FileBase64,
                    Base64Image = mauChuKy?.ImageBase64,
                    UserId = visnamAccount.Key,
                    UserKey = decryptedSecret,
                    TextOut = cts.SubjectName, // Sử dụng SubjectName của chứng thư số làm text hiển thị
                    SignatureName = "Signature",
                    AppendDateSign = true,
                    TypeSignature = 1,
                    TextLocationIdentifier = "SIGN"
                };

                // Thiết lập vị trí và kích thước chữ ký dựa trên loại ký
                if (mauChuKy != null)
                {
                    if (mauChuKy.LoaiKySuDung == (short)LoaiKyEnum.KyNhay)
                    {
                        signPdfRequest.XPoint = 500;
                        signPdfRequest.YPoint = 190;
                        signPdfRequest.Width = 100;
                        signPdfRequest.Height = 50;
                    }
                    else
                    {
                        signPdfRequest.XPoint = 395;
                        signPdfRequest.YPoint = 100;
                        signPdfRequest.Width = 200;
                        signPdfRequest.Height = 100;
                    }
                }

                // Gọi hàm SignPDFSync
                var signResult = _client.SignPDFSync(signPdfRequest);

                // Kiểm tra kết quả
                if (signResult.Status != 0)
                {
                    throw new Exception($"Ký PDF thất bại: {signResult.Error} (Status: {signResult.Status})");
                }

                if (string.IsNullOrEmpty(signResult.Obj))
                {
                    throw new Exception("API trả về file PDF đã ký rỗng");
                }

                Log.Information($"Ký PDF thành công cho chứng thư số ID: {model.ChungThuSoId}");

                // Trả về kết quả
                return new SignDocumentResponseModel()
                {
                    FileBase64 = signResult.Obj
                };
            }
            catch (Exception ex)
            {
                Log.Error($"Có lỗi xảy ra khi ký PDF: {ex.Message}", ex);
                throw;
            }

            #endregion
        }
    }
}