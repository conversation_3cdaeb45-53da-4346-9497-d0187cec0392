using Core.Shared.Model;

namespace Core.Business.Signing;

public class SignDocumentRequestModel
{
    /// <summary>
    /// File cần ký
    /// </summary>
    public string FileBase64 { get; set; }
    
    /// <summary>
    /// Id chứng thư số thực hiện ký
    /// </summary>
    public int ChungThuSoId { get; set; }
    
    /// <summary>
    /// Id mẫu chữ ký của người dùng
    /// </summary>
    public int? MauChuKyId { get; set; }
    
    /// <summary>
    /// Cấu hình hiển thị chữ ký trên tài liệu cần ký
    /// </summary>
    public SignatureAppearanceConfiguration SignatureAppearanceConfiguration { get; set; }
}