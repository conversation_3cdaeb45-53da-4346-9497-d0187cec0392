using Core.Business.Core.LockDynamic;
using Core.Business.Core;
using Core.Business.ThirdPartyBussiness.SignServer;
using Microsoft.Extensions.DependencyInjection;
using Core.Business.Workflow.Implementation;
using Core.Business.Workflow.Interface;
using Core.Business.Workflow;
using Core.Shared.Constants;
using Microsoft.Extensions.Configuration;
using Core.Shared.Infrastructure;

namespace Core.Business
{
    public static class ServiceConfigure
    {
        public static void Configure(IServiceCollection services, IConfiguration configuration)
        {
            VisnamSignServerClient.ServiceConfigure.Configure(services, configuration);

            // Apply MediaR Scanning to find all assemblies Requests and Responses
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ServiceConfigure).Assembly));

            services.AddScoped<ISendMailHandler, SendMailHandler>();
            services.AddScoped<ICallStoreHelper, CallStoreHelper>();
            services.AddTransient<ILdapManager, LdapManager>();
            services.AddTransient<IRedisHandler, RedisHandler>();
            services.AddTransient<ILockDynamicHandler, LockDynamicHandler>();
            services.AddTransient<IAuthHandler, DBAuthHandler>();

            //services.AddScoped<IPersistenceProviderContainer, PersistenceProviderContainer>();

            //WorkflowInit.ServiceProvider = services.BuildServiceProvider();

            services.AddNamedTransient<ISignServerService, VisnamSignServerService>(SignServerServiceContants.Visnam);
        }
    }
}
