 public void HSM_Visnam_Sign_Test()
 {
     var client = new HttpClient();
     var path = "/api/v2/pdf/sign/originaldata";
     var schema = Visnam_Schema;
     var port = Visnam_Port;
     var domain = Visnam_Domain;
     var host = $"{schema}://{domain}:{port}";
     var url = $"{host}{path}";
     var request = new HttpRequestMessage(HttpMethod.Post, url);
     var media_type = "application/json";
     var hash_ins = new HashInstance
     {
         method = "POST",
         schema = schema,
         host = domain,
         port = port,
         path = path,
         contentType = $"{media_type}; charset=utf-8",
         userId = Visnam_UserId,  // user name trong db
         nonce = "57gkizxm79rilnfd9afk68rwyb1lkraa", // giá trị ngẫu nhiên (128 bit) do client sinh ra, không trùng lặp với các lần gọi khác
         sharedKey = Visnam_UserKey // pass trong db
     };
     var now = DateTime.Now;
     var time = now.ToString("R");
     var start_date = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
     var tick_time = (long)((now - start_date).TotalSeconds);
     //var file_path = "Test_chuky.pdf";
     var file_path = "KY_TET.pdf";
     var image_file_path = "30942_laihuyhoang.png";
     var base_64_file_content = Convert.ToBase64String(File.ReadAllBytes(file_path));
     var base_64_image_content = Convert.ToBase64String(File.ReadAllBytes(image_file_path));

     var hash_content_obj = new PdfSigningContent
     {
         base64image = base_64_image_content,
         base64pdf = base_64_file_content,
         hashalg = "SHA256",
         textout = "BS. Lại Huy Hoàng",
         signaturename = "Signature",
         xpoint = 50,
         ypoint = 50,
         width = 200,
         height = 100,
         AppendDateSign = true,
         typesignature = 1,
         //FontSize = 10.0f,
         //textoutcolor = "Black"
         //fontstyle = 1
         TextLocationIdentifier = "SIGN",
     };
     var hash_content_json = JsonConvert.SerializeObject(hash_content_obj);
     var auth_value = hash_ins.GetHmacSha256(time, hash_content_json);

     request.Headers.Add("Authorization", $"HmacSHA256 {hash_ins.userId}:{hash_ins.nonce}:{auth_value}:{tick_time}");
     var content = new StringContent(hash_content_json, Encoding.UTF8, media_type);
     request.Content = content;
     var response = client.SendAsync(request).Result;
     // response.EnsureSuccessStatusCode();
     var response_text = response.Content.ReadAsStringAsync().Result;
     var result_obj = JsonConvert.DeserializeObject<PdfSignedContent>(response_text);
     /** Visnam error:
     description: "Xác thực không thành công. Chữ ký HMAC không hợp lệ"
     error: "Xác thực không thành công"
     status: 2010
     **/
     Assert.AreEqual(0, result_obj.status);
     Assert.IsNotNull(result_obj.obj);
     var file_bytes = Convert.FromBase64String(result_obj.obj);
     var name = "sign_result_visnam" + DateTime.Now.ToString("ddMMyyyyHHmmss") + ".pdf";
     File.WriteAllBytes(name, file_bytes);

 }