using System.Text.Json.Serialization;
using System;
using System.Collections.Generic;

namespace VisnamSignServerClient
{
    public class VisnamSignServerClientConfiguration
    {
        public string Domain { get; set; }
        public string Schema  { get; set; }
        public int Port { get; set; }
    }

    public class HouResponseDataModel<T>
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
    }

    #region EndCert

    public class EndCertRequestModel
    {
        public string UserId { get; set; }
        public string UserKey { get; set; }
    }
    
    public class EndCertResponseModel
    {
        [JsonPropertyName("data")]
        public string Data { get; set; }
    }

    #endregion

    #region SignHash

    public class SignHashDataClientRequestModel
    {
        public string Base64Hash { get; set; }
        
        public string UserId { get; set; }
        
        public string UserKey { get; set; }
    }
    
    public class SignHashRequestModel
    {
        [JsonPropertyName("hashalg")]
        public string HashAlg { get; set; } = "SHA256";
        
        [JsonPropertyName("base64hash")]
        public string Base64Hash { get; set; }
    }
    
    public class SignHashResponseModel
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        [JsonPropertyName("description")]
        public string Description { get; set; }
        
        [JsonPropertyName("error")]
        public string Error { get; set; }
        
        [JsonPropertyName("obj")]
        public string Obj { get; set; }
    }

    #endregion

    #region SignPDF

    public class SignPDFSyncRequestModel
    {
        public string Base64Image { get; set; }
        public string Base64Pdf { get; set; }
        public string TextOut { get; set; }
        public string SignatureName { get; set; } = "Signature";
        public int XPoint { get; set; } = 50;
        public int YPoint { get; set; } = 50;
        public int Width { get; set; } = 200;
        public int Height { get; set; } = 100;
        public bool AppendDateSign { get; set; } = true;
        public int TypeSignature { get; set; } = 1;
        public string TextLocationIdentifier { get; set; } = "SIGN";
        public string UserId { get; set; }
        public string UserKey { get; set; }
    }

    public class PdfSigningContent
    {
        [JsonPropertyName("base64image")]
        public string Base64Image { get; set; }

        [JsonPropertyName("base64pdf")]
        public string Base64Pdf { get; set; }

        [JsonPropertyName("hashalg")]
        public string HashAlg { get; set; } = "SHA256";

        [JsonPropertyName("textout")]
        public string TextOut { get; set; }

        [JsonPropertyName("signaturename")]
        public string SignatureName { get; set; } = "Signature";

        [JsonPropertyName("xpoint")]
        public int XPoint { get; set; } = 50;

        [JsonPropertyName("ypoint")]
        public int YPoint { get; set; } = 50;

        [JsonPropertyName("width")]
        public int Width { get; set; } = 200;

        [JsonPropertyName("height")]
        public int Height { get; set; } = 100;

        [JsonPropertyName("AppendDateSign")]
        public bool AppendDateSign { get; set; } = true;

        [JsonPropertyName("typesignature")]
        public int TypeSignature { get; set; } = 1;

        [JsonPropertyName("TextLocationIdentifier")]
        public string TextLocationIdentifier { get; set; } = "SIGN";
    }

    public class PdfSignedContent
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("error")]
        public string Error { get; set; }

        [JsonPropertyName("obj")]
        public string Obj { get; set; }
    }

    #endregion
}