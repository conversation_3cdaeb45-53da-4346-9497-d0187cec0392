using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace VisnamSignServerClient
{
    public class VisnamSignServerApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly VisnamSignServerClientConfiguration _config;

        public VisnamSignServerApiClient(VisnamSignServerClientConfiguration config, HttpClient httpClient)
        {
            _config = config;
            _httpClient = httpClient;
        }

        /// <summary>
        /// L<PERSON>y chứng thư số cuối của người dùng
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<EndCertResponseModel> GetEndCertAsync(EndCertRequestModel requestModel)
        {
            var path = VisnamConstants.EndCertUrl;
            var schema = _config.Schema;
            var port = _config.Port;
            var domain = _config.Domain;
            var host = $"{schema}://{domain}:{port}";
            var url = $"{host}{path}";
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            var mediaType = "application/json";
            var hashIns = new HashInstance
            {
                Method = "GET",
                Schema = schema,
                Host = domain,
                Port = port,
                Path = path,
                ContentType = $"{mediaType}; charset=utf-8",
                UserId = requestModel.UserId, // user name trong db
                Nonce = VisnamUtils
                    .GenerateUnique128Bit(), // giá trị ngẫu nhiên (128 bit) do client sinh ra, không trùng lặp với các lần gọi khác
                SharedKey = requestModel.UserKey // pass trong db
            };

            var now = DateTime.Now;
            var time = now.ToString("R");
            var startDate = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            var tickTime = (long)((now - startDate).TotalSeconds);
            var hashContentJson = "";
            var authValue = hashIns.GetHmacSha256(time, hashContentJson);

            request.Headers.Add("Authorization", $"HmacSHA256 {hashIns.UserId}:{hashIns.Nonce}:{authValue}:{tickTime}");

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(content))
                {
                    throw new Exception("Data is null");
                }

                // var rs = System.Text.Json.JsonSerializer.Deserialize<EndCertResponseModel>(content);
                return new EndCertResponseModel() { Data = content };
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }
        
        // Ký hash
        public async Task<SignHashResponseModel> SignHashAsync(SignHashDataClientRequestModel requestModel)
        {
            var path = VisnamConstants.PdfSignHashDataUrl;
            var schema = _config.Schema;
            var port = _config.Port;
            var domain = _config.Domain;
            var host = $"{schema}://{domain}:{port}";
            var url = $"{host}{path}";
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            var mediaType = "application/json";
            var hashIns = new HashInstance
            {
                Method = "POST",
                Schema = schema,
                Host = domain,
                Port = port,
                Path = path,
                ContentType = $"{mediaType}; charset=utf-8",
                UserId = requestModel.UserId, // user name trong db
                Nonce = VisnamUtils
                    .GenerateUnique128Bit(), // giá trị ngẫu nhiên (128 bit) do client sinh ra, không trùng lặp với các lần gọi khác
                SharedKey = requestModel.UserKey // pass trong db
            };

            var now = DateTime.Now;
            var time = now.ToString("R");
            var startDate = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            var tickTime = (long)((now - startDate).TotalSeconds);
            
            var dataToSign = new SignHashRequestModel()
            {
                Base64Hash = requestModel.Base64Hash,
                HashAlg = "SHA256"
            };
            var hashContentJson = System.Text.Json.JsonSerializer.Serialize(dataToSign);
            
            var authValue = hashIns.GetHmacSha256(time, hashContentJson);
            request.Headers.Add("Authorization", $"HmacSHA256 {hashIns.UserId}:{hashIns.Nonce}:{authValue}:{tickTime}");

            var content = new StringContent(hashContentJson, Encoding.UTF8, mediaType);
            request.Content = content;

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var readAsString = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(readAsString))
                {
                    throw new Exception("Data is null");
                }

                var rs = System.Text.Json.JsonSerializer.Deserialize<SignHashResponseModel>(readAsString);
                if (rs.Status != 0)
                {
                    throw new Exception(rs.Error);
                }
                return rs;
            }
            else
            {
                throw new Exception("Failed to sign hash");
            }
        }

        /// <summary>
        /// Ký PDF đồng bộ
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public PdfSignedContent SignPDFSync(SignPDFSyncRequestModel requestModel)
        {
            var path = VisnamConstants.PdfSignOriginalDataUrl;
            var schema = _config.Schema;
            var port = _config.Port;
            var domain = _config.Domain;
            var host = $"{schema}://{domain}:{port}";
            var url = $"{host}{path}";
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            var mediaType = "application/json";
            var hashIns = new HashInstance
            {
                Method = "POST",
                Schema = schema,
                Host = domain,
                Port = port,
                Path = path,
                ContentType = $"{mediaType}; charset=utf-8",
                UserId = requestModel.UserId, // user name trong db
                Nonce = VisnamUtils
                    .GenerateUnique128Bit(), // giá trị ngẫu nhiên (128 bit) do client sinh ra, không trùng lặp với các lần gọi khác
                SharedKey = requestModel.UserKey // pass trong db
            };

            var now = DateTime.Now;
            var time = now.ToString("R");
            var startDate = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            var tickTime = (long)((now - startDate).TotalSeconds);

            var dataToSign = new PdfSigningContent()
            {
                Base64Image = requestModel.Base64Image,
                Base64Pdf = requestModel.Base64Pdf,
                HashAlg = "SHA256",
                TextOut = requestModel.TextOut,
                SignatureName = requestModel.SignatureName,
                XPoint = requestModel.XPoint,
                YPoint = requestModel.YPoint,
                Width = requestModel.Width,
                Height = requestModel.Height,
                AppendDateSign = requestModel.AppendDateSign,
                TypeSignature = requestModel.TypeSignature,
                TextLocationIdentifier = requestModel.TextLocationIdentifier
            };
            var hashContentJson = System.Text.Json.JsonSerializer.Serialize(dataToSign);

            var authValue = hashIns.GetHmacSha256(time, hashContentJson);
            request.Headers.Add("Authorization", $"HmacSHA256 {hashIns.UserId}:{hashIns.Nonce}:{authValue}:{tickTime}");

            var content = new StringContent(hashContentJson, Encoding.UTF8, mediaType);
            request.Content = content;

            var response = _httpClient.SendAsync(request).Result;

            if (response.IsSuccessStatusCode)
            {
                var readAsString = response.Content.ReadAsStringAsync().Result;
                if (string.IsNullOrEmpty(readAsString))
                {
                    throw new Exception("Data is null");
                }

                var rs = System.Text.Json.JsonSerializer.Deserialize<PdfSignedContent>(readAsString);
                if (rs.Status != 0)
                {
                    throw new Exception(rs.Error);
                }
                return rs;
            }
            else
            {
                throw new Exception("Failed to sign PDF");
            }
        }
    }
}